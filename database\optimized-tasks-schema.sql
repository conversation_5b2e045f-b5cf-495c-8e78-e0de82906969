-- =====================================================
-- OPTIMIZED TASKS & TASK_STATUS_HISTORY TABLES
-- Mock veri ile uyumlu hale getirilmiş yapı
-- =====================================================

-- Önce mevcut tabloları yedekle ve kaldır
DROP TABLE IF EXISTS public.task_status_history CASCADE;
DROP TABLE IF EXISTS public.tasks CASCADE;

-- =====================================================
-- 1. OPTIMIZED TASKS TABLE
-- =====================================================
CREATE TABLE public.tasks (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  
  -- İlişkiler
  case_id UUID REFERENCES public.cases(id) ON DELETE CASCADE,
  assignee_id UUID REFERENCES public.users(id) ON DELETE SET NULL,
  created_by_id UUID REFERENCES public.users(id),
  
  -- Görev bilgileri (Mock veri ile uyumlu)
  task_type TEXT NOT NULL CHECK (task_type IN (
    'PICK_UP_FROM_MORGUE', 'TO_AIRPORT', 'TO_CONSULATE', 'DELIVERED',
    'DOCUMENT_DELIVERY', 'FAMILY_MEETING', 'HOSPITAL_TRANSFER',
    'BURIAL_PREPARATION', 'CEREMONY_SETUP', 'TRANSPORT'
  )),
  title VARCHAR(255) NOT NULL,
  description TEXT,
  
  -- Durum ve öncelik (Mock veri ile uyumlu)
  status TEXT DEFAULT 'PENDING' CHECK (status IN (
    'PENDING', 'IN_PROGRESS', 'COMPLETED', 'FAILED', 'OVERDUE', 'CANCELLED'
  )),
  sub_status TEXT CHECK (sub_status IN (
    'PICKED_UP', 'IN_TRANSIT', 'DELIVERED', 'ON_HOLD', 'WAITING_APPROVAL'
  )),
  priority TEXT DEFAULT 'MEDIUM' CHECK (priority IN (
    'LOW', 'MEDIUM', 'HIGH', 'URGENT'
  )),
  
  -- İlerleme ve süre bilgileri
  progress INTEGER DEFAULT 0 CHECK (progress >= 0 AND progress <= 100),
  estimated_duration VARCHAR(50), -- "2 saat", "30 dakika" gibi
  
  -- Zaman bilgileri
  scheduled_at TIMESTAMP WITH TIME ZONE,
  due_time TIME, -- "14:30" gibi
  started_at TIMESTAMP WITH TIME ZONE,
  completed_at TIMESTAMP WITH TIME ZONE,
  
  -- Konum bilgileri
  location TEXT, -- "Fatih Camii → Karacaahmet" gibi
  pickup_location TEXT,
  delivery_location TEXT,
  current_latitude DECIMAL(10, 8),
  current_longitude DECIMAL(11, 8),
  
  -- Notlar ve metadata
  notes TEXT,
  case_name VARCHAR(255), -- Denormalized for quick access
  
  -- Audit fields
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- =====================================================
-- 2. OPTIMIZED TASK_STATUS_HISTORY TABLE
-- =====================================================
CREATE TABLE public.task_status_history (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  task_id UUID REFERENCES public.tasks(id) ON DELETE CASCADE,
  actor_id UUID REFERENCES public.users(id),
  
  -- Durum değişiklikleri
  old_status TEXT,
  new_status TEXT,
  old_sub_status TEXT,
  new_sub_status TEXT,
  old_priority TEXT,
  new_priority TEXT,
  old_progress INTEGER,
  new_progress INTEGER,
  
  -- Konum bilgisi (değişiklik anında)
  location_latitude DECIMAL(10, 8),
  location_longitude DECIMAL(11, 8),
  location_description TEXT,
  
  -- Notlar ve açıklamalar
  change_reason TEXT,
  notes TEXT,
  
  -- Metadata
  changed_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  device_info JSONB -- Cihaz bilgileri (mobile app için)
);

-- =====================================================
-- 3. INDEXES FOR PERFORMANCE
-- =====================================================

-- Tasks tablosu için indexler
CREATE INDEX idx_tasks_case_id ON public.tasks(case_id);
CREATE INDEX idx_tasks_assignee_id ON public.tasks(assignee_id);
CREATE INDEX idx_tasks_status ON public.tasks(status);
CREATE INDEX idx_tasks_priority ON public.tasks(priority);
CREATE INDEX idx_tasks_created_at ON public.tasks(created_at);
CREATE INDEX idx_tasks_due_time ON public.tasks(due_time);
CREATE INDEX idx_tasks_progress ON public.tasks(progress);

-- Task status history tablosu için indexler
CREATE INDEX idx_task_history_task_id ON public.task_status_history(task_id);
CREATE INDEX idx_task_history_actor_id ON public.task_status_history(actor_id);
CREATE INDEX idx_task_history_changed_at ON public.task_status_history(changed_at);

-- =====================================================
-- 4. TRIGGERS FOR AUTOMATION
-- =====================================================

-- Updated_at otomatik güncelleme trigger'ı
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_tasks_updated_at 
    BEFORE UPDATE ON public.tasks 
    FOR EACH ROW 
    EXECUTE FUNCTION update_updated_at_column();

-- Task status değişikliklerini otomatik kaydetme trigger'ı
CREATE OR REPLACE FUNCTION log_task_status_change()
RETURNS TRIGGER AS $$
BEGIN
    -- Sadece status, sub_status, priority veya progress değişirse kaydet
    IF (OLD.status IS DISTINCT FROM NEW.status) OR 
       (OLD.sub_status IS DISTINCT FROM NEW.sub_status) OR 
       (OLD.priority IS DISTINCT FROM NEW.priority) OR 
       (OLD.progress IS DISTINCT FROM NEW.progress) THEN
        
        INSERT INTO public.task_status_history (
            task_id, 
            actor_id,
            old_status, 
            new_status,
            old_sub_status, 
            new_sub_status,
            old_priority,
            new_priority,
            old_progress,
            new_progress,
            change_reason
        ) VALUES (
            NEW.id,
            NEW.assignee_id, -- Varsayılan olarak assignee'yi actor olarak kabul et
            OLD.status,
            NEW.status,
            OLD.sub_status,
            NEW.sub_status,
            OLD.priority,
            NEW.priority,
            OLD.progress,
            NEW.progress,
            'Automatic status change'
        );
    END IF;
    
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER log_task_status_change_trigger
    AFTER UPDATE ON public.tasks
    FOR EACH ROW
    EXECUTE FUNCTION log_task_status_change();

-- =====================================================
-- 5. RLS POLICIES
-- =====================================================

-- Tasks tablosu için RLS
ALTER TABLE public.tasks ENABLE ROW LEVEL SECURITY;

-- ADMIN: Tüm görevleri görebilir ve düzenleyebilir
CREATE POLICY "Admin can manage all tasks" ON public.tasks
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM public.users 
            WHERE id = auth.uid() AND role = 'ADMIN'
        )
    );

-- DRIVER: Sadece kendisine atanan görevleri görebilir ve düzenleyebilir
CREATE POLICY "Driver can manage assigned tasks" ON public.tasks
    FOR ALL USING (
        assignee_id = auth.uid() AND
        EXISTS (
            SELECT 1 FROM public.users 
            WHERE id = auth.uid() AND role = 'DRIVER'
        )
    );

-- FAMILY: Sadece kendi vakalarının görevlerini görebilir
CREATE POLICY "Family can view their case tasks" ON public.tasks
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM public.cases c
            JOIN public.users u ON c.family_contact_id = u.id
            WHERE c.id = case_id AND u.id = auth.uid() AND u.role = 'FAMILY'
        )
    );

-- Task status history tablosu için RLS
ALTER TABLE public.task_status_history ENABLE ROW LEVEL SECURITY;

-- ADMIN: Tüm geçmişi görebilir
CREATE POLICY "Admin can view all task history" ON public.task_status_history
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM public.users 
            WHERE id = auth.uid() AND role = 'ADMIN'
        )
    );

-- DRIVER: Sadece kendi görevlerinin geçmişini görebilir
CREATE POLICY "Driver can view assigned task history" ON public.task_status_history
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM public.tasks t
            WHERE t.id = task_id AND t.assignee_id = auth.uid()
        ) AND
        EXISTS (
            SELECT 1 FROM public.users 
            WHERE id = auth.uid() AND role = 'DRIVER'
        )
    );

-- FAMILY: Kendi vakalarının görev geçmişini görebilir
CREATE POLICY "Family can view their case task history" ON public.task_status_history
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM public.tasks t
            JOIN public.cases c ON t.case_id = c.id
            JOIN public.users u ON c.family_contact_id = u.id
            WHERE t.id = task_id AND u.id = auth.uid() AND u.role = 'FAMILY'
        )
    );

-- =====================================================
-- 6. HELPER FUNCTIONS
-- =====================================================

-- Görev ilerlemesini otomatik hesaplama fonksiyonu
CREATE OR REPLACE FUNCTION calculate_task_progress(task_id UUID)
RETURNS INTEGER AS $$
DECLARE
    task_status TEXT;
    task_sub_status TEXT;
    calculated_progress INTEGER := 0;
BEGIN
    SELECT status, sub_status INTO task_status, task_sub_status
    FROM public.tasks WHERE id = task_id;
    
    -- Status'a göre temel ilerleme
    CASE task_status
        WHEN 'PENDING' THEN calculated_progress := 0;
        WHEN 'IN_PROGRESS' THEN 
            CASE task_sub_status
                WHEN 'PICKED_UP' THEN calculated_progress := 25;
                WHEN 'IN_TRANSIT' THEN calculated_progress := 50;
                WHEN 'DELIVERED' THEN calculated_progress := 75;
                ELSE calculated_progress := 10;
            END CASE;
        WHEN 'COMPLETED' THEN calculated_progress := 100;
        WHEN 'FAILED' THEN calculated_progress := 0;
        WHEN 'CANCELLED' THEN calculated_progress := 0;
        ELSE calculated_progress := 0;
    END CASE;
    
    RETURN calculated_progress;
END;
$$ LANGUAGE plpgsql;

-- Görev durumunu otomatik güncelleme fonksiyonu
CREATE OR REPLACE FUNCTION auto_update_task_status()
RETURNS TRIGGER AS $$
BEGIN
    -- Eğer progress 100 ise status'u COMPLETED yap
    IF NEW.progress = 100 AND NEW.status != 'COMPLETED' THEN
        NEW.status := 'COMPLETED';
        NEW.completed_at := NOW();
    END IF;
    
    -- Eğer due_time geçmişse ve status hala PENDING ise OVERDUE yap
    IF NEW.due_time < CURRENT_TIME AND NEW.status = 'PENDING' THEN
        NEW.status := 'OVERDUE';
    END IF;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER auto_update_task_status_trigger
    BEFORE UPDATE ON public.tasks
    FOR EACH ROW
    EXECUTE FUNCTION auto_update_task_status();
