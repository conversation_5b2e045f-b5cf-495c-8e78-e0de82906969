-- ================================
-- ROW LEVEL SECURITY POLICIES
-- ================================
-- Based on DATABASE-SYSTEM.md specifications
-- Created: 2025-01-27

-- Enable RLS on all tables
ALTER TABLE users ENABLE ROW LEVEL SECURITY;
ALTER TABLE deceased ENABLE ROW LEVEL SECURITY;
ALTER TABLE cases ENABLE ROW LEVEL SECURITY;
ALTER TABLE documents ENABLE ROW LEVEL SECURITY;
ALTER TABLE notifications ENABLE ROW LEVEL SECURITY;
ALTER TABLE lookup_values ENABLE ROW LEVEL SECURITY;
ALTER TABLE places ENABLE ROW LEVEL SECURITY;
ALTER TABLE tasks ENABLE ROW LEVEL SECURITY;
ALTER TABLE driver_locations ENABLE ROW LEVEL SECURITY;
ALTER TABLE task_events ENABLE ROW LEVEL SECURITY;

-- ================================
-- USERS TABLE POLICIES
-- ================================

-- Users can view their own profile
CREATE POLICY "Users can view own profile" ON users
  FOR SELECT USING (auth.uid() = id);

-- Admins can view all users
CREATE POLICY "Admins can view all users" ON users
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM users 
      WHERE id = auth.uid() AND role = 'ADMIN'
    )
  );

-- Users can update their own profile
CREATE POLICY "Users can update own profile" ON users
  FOR UPDATE USING (auth.uid() = id);

-- Admins can insert new users
CREATE POLICY "Admins can insert users" ON users
  FOR INSERT WITH CHECK (
    EXISTS (
      SELECT 1 FROM users 
      WHERE id = auth.uid() AND role = 'ADMIN'
    )
  );

-- ================================
-- DECEASED TABLE POLICIES
-- ================================

-- Admins can view all deceased records
CREATE POLICY "Admins can view all deceased" ON deceased
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM users 
      WHERE id = auth.uid() AND role = 'ADMIN'
    )
  );

-- Family members can view deceased records for their cases
CREATE POLICY "Family can view their deceased" ON deceased
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM cases c
      JOIN users u ON u.id = c.family_user_id
      WHERE c.deceased_id = deceased.id 
        AND u.id = auth.uid() 
        AND u.role = 'FAMILY'
    )
  );

-- Drivers can view deceased records for their assigned tasks
CREATE POLICY "Drivers can view assigned deceased" ON deceased
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM tasks t
      JOIN cases c ON c.id = t.case_id
      JOIN users u ON u.id = t.assignee_id
      WHERE c.deceased_id = deceased.id 
        AND u.id = auth.uid() 
        AND u.role = 'DRIVER'
    )
  );

-- Admins can insert/update deceased records
CREATE POLICY "Admins can manage deceased" ON deceased
  FOR ALL USING (
    EXISTS (
      SELECT 1 FROM users 
      WHERE id = auth.uid() AND role = 'ADMIN'
    )
  );

-- ================================
-- CASES TABLE POLICIES
-- ================================

-- Admins can view all cases
CREATE POLICY "Admins can view all cases" ON cases
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM users 
      WHERE id = auth.uid() AND role = 'ADMIN'
    )
  );

-- Family members can view their own cases
CREATE POLICY "Family can view own cases" ON cases
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM users 
      WHERE id = auth.uid() AND id = cases.family_user_id AND role = 'FAMILY'
    )
  );

-- Drivers can view cases for their assigned tasks
CREATE POLICY "Drivers can view assigned cases" ON cases
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM tasks t
      JOIN users u ON u.id = t.assignee_id
      WHERE t.case_id = cases.id 
        AND u.id = auth.uid() 
        AND u.role = 'DRIVER'
    )
  );

-- Admins can manage cases
CREATE POLICY "Admins can manage cases" ON cases
  FOR ALL USING (
    EXISTS (
      SELECT 1 FROM users 
      WHERE id = auth.uid() AND role = 'ADMIN'
    )
  );

-- ================================
-- DOCUMENTS TABLE POLICIES
-- ================================

-- Admins can view all documents
CREATE POLICY "Admins can view all documents" ON documents
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM users 
      WHERE id = auth.uid() AND role = 'ADMIN'
    )
  );

-- Family members can view documents for their cases
CREATE POLICY "Family can view case documents" ON documents
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM cases c
      JOIN users u ON u.id = c.family_user_id
      WHERE c.id = documents.case_id 
        AND u.id = auth.uid() 
        AND u.role = 'FAMILY'
    )
  );

-- Drivers can view documents for their assigned tasks
CREATE POLICY "Drivers can view assigned documents" ON documents
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM tasks t
      JOIN users u ON u.id = t.assignee_id
      WHERE t.case_id = documents.case_id 
        AND u.id = auth.uid() 
        AND u.role = 'DRIVER'
    )
  );

-- Admins and drivers can upload documents
CREATE POLICY "Admins and drivers can upload documents" ON documents
  FOR INSERT WITH CHECK (
    EXISTS (
      SELECT 1 FROM users 
      WHERE id = auth.uid() AND role IN ('ADMIN', 'DRIVER')
    )
  );

-- ================================
-- NOTIFICATIONS TABLE POLICIES
-- ================================

-- Admins can view all notifications
CREATE POLICY "Admins can view all notifications" ON notifications
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM users 
      WHERE id = auth.uid() AND role = 'ADMIN'
    )
  );

-- Family members can view notifications for their cases
CREATE POLICY "Family can view case notifications" ON notifications
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM cases c
      JOIN users u ON u.id = c.family_user_id
      WHERE c.id = notifications.case_id 
        AND u.id = auth.uid() 
        AND u.role = 'FAMILY'
    )
  );

-- Drivers can view notifications for their assigned tasks
CREATE POLICY "Drivers can view assigned notifications" ON notifications
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM tasks t
      JOIN users u ON u.id = t.assignee_id
      WHERE t.case_id = notifications.case_id 
        AND u.id = auth.uid() 
        AND u.role = 'DRIVER'
    )
  );

-- Admins can manage notifications
CREATE POLICY "Admins can manage notifications" ON notifications
  FOR ALL USING (
    EXISTS (
      SELECT 1 FROM users 
      WHERE id = auth.uid() AND role = 'ADMIN'
    )
  );

-- ================================
-- LOOKUP_VALUES TABLE POLICIES
-- ================================

-- All authenticated users can read lookup values
CREATE POLICY "All users can read lookup values" ON lookup_values
  FOR SELECT USING (auth.uid() IS NOT NULL);

-- Only admins can manage lookup values
CREATE POLICY "Admins can manage lookup values" ON lookup_values
  FOR ALL USING (
    EXISTS (
      SELECT 1 FROM users 
      WHERE id = auth.uid() AND role = 'ADMIN'
    )
  );

-- ================================
-- PLACES TABLE POLICIES
-- ================================

-- All authenticated users can read places
CREATE POLICY "All users can read places" ON places
  FOR SELECT USING (auth.uid() IS NOT NULL);

-- Only admins can manage places
CREATE POLICY "Admins can manage places" ON places
  FOR ALL USING (
    EXISTS (
      SELECT 1 FROM users 
      WHERE id = auth.uid() AND role = 'ADMIN'
    )
  );

-- ================================
-- TASKS TABLE POLICIES
-- ================================

-- Admins can view all tasks
CREATE POLICY "Admins can view all tasks" ON tasks
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM users 
      WHERE id = auth.uid() AND role = 'ADMIN'
    )
  );

-- Drivers can view their assigned tasks
CREATE POLICY "Drivers can view assigned tasks" ON tasks
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM users 
      WHERE id = auth.uid() AND id = tasks.assignee_id AND role = 'DRIVER'
    )
  );

-- Family members can view tasks for their cases
CREATE POLICY "Family can view case tasks" ON tasks
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM cases c
      JOIN users u ON u.id = c.family_user_id
      WHERE c.id = tasks.case_id 
        AND u.id = auth.uid() 
        AND u.role = 'FAMILY'
    )
  );

-- Admins can manage tasks
CREATE POLICY "Admins can manage tasks" ON tasks
  FOR ALL USING (
    EXISTS (
      SELECT 1 FROM users 
      WHERE id = auth.uid() AND role = 'ADMIN'
    )
  );

-- Drivers can update their assigned tasks
CREATE POLICY "Drivers can update assigned tasks" ON tasks
  FOR UPDATE USING (
    EXISTS (
      SELECT 1 FROM users 
      WHERE id = auth.uid() AND id = tasks.assignee_id AND role = 'DRIVER'
    )
  );

-- ================================
-- DRIVER_LOCATIONS TABLE POLICIES
-- ================================

-- Admins can view all driver locations
CREATE POLICY "Admins can view all driver locations" ON driver_locations
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM users 
      WHERE id = auth.uid() AND role = 'ADMIN'
    )
  );

-- Drivers can view their own locations
CREATE POLICY "Drivers can view own locations" ON driver_locations
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM users 
      WHERE id = auth.uid() AND id = driver_locations.driver_id AND role = 'DRIVER'
    )
  );

-- Drivers can insert their own locations
CREATE POLICY "Drivers can insert own locations" ON driver_locations
  FOR INSERT WITH CHECK (
    EXISTS (
      SELECT 1 FROM users 
      WHERE id = auth.uid() AND id = driver_locations.driver_id AND role = 'DRIVER'
    )
  );

-- Family members can view driver locations for their cases
CREATE POLICY "Family can view case driver locations" ON driver_locations
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM tasks t
      JOIN cases c ON c.id = t.case_id
      JOIN users u ON u.id = c.family_user_id
      WHERE t.id = driver_locations.task_id 
        AND u.id = auth.uid() 
        AND u.role = 'FAMILY'
    )
  );

-- ================================
-- TASK_EVENTS TABLE POLICIES
-- ================================

-- Admins can view all task events
CREATE POLICY "Admins can view all task events" ON task_events
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM users 
      WHERE id = auth.uid() AND role = 'ADMIN'
    )
  );

-- Drivers can view events for their assigned tasks
CREATE POLICY "Drivers can view assigned task events" ON task_events
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM tasks t
      JOIN users u ON u.id = t.assignee_id
      WHERE t.id = task_events.task_id 
        AND u.id = auth.uid() 
        AND u.role = 'DRIVER'
    )
  );

-- Family members can view task events for their cases
CREATE POLICY "Family can view case task events" ON task_events
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM tasks t
      JOIN cases c ON c.id = t.case_id
      JOIN users u ON u.id = c.family_user_id
      WHERE t.id = task_events.task_id 
        AND u.id = auth.uid() 
        AND u.role = 'FAMILY'
    )
  );

-- Admins and drivers can insert task events
CREATE POLICY "Admins and drivers can insert task events" ON task_events
  FOR INSERT WITH CHECK (
    EXISTS (
      SELECT 1 FROM users 
      WHERE id = auth.uid() AND role IN ('ADMIN', 'DRIVER')
    )
  );
