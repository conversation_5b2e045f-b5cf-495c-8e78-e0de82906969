-- OPTIMIZE EDİLMİŞ VERİTABANI YAPISINA MİGRASYON SCRIPT'İ
-- Mevcut verilerden yeni optimize edilmiş yapıya geçiş
-- Tarih: 2024-01-20
-- Versiyon: 1.0

-- =====================================================
-- BACKUP MEVCUT VERİLER
-- =====================================================

-- Mevcut verileri yedekle
CREATE TABLE IF NOT EXISTS backup_users AS SELECT * FROM users;
CREATE TABLE IF NOT EXISTS backup_deceased AS SELECT * FROM deceased;
CREATE TABLE IF NOT EXISTS backup_cases AS SELECT * FROM cases;
CREATE TABLE IF NOT EXISTS backup_tasks AS SELECT * FROM tasks;
CREATE TABLE IF NOT EXISTS backup_documents AS SELECT * FROM documents;
CREATE TABLE IF NOT EXISTS backup_task_status_history AS SELECT * FROM task_status_history;
CREATE TABLE IF NOT EXISTS backup_notifications AS SELECT * FROM notifications;
CREATE TABLE IF NOT EXISTS backup_lookup_values AS SELECT * FROM lookup_values;

-- =====================================================
-- MEVCUT TABLOLARI GÜNCELLE
-- =====================================================

-- USERS tablosunu güncelle
ALTER TABLE users 
ADD COLUMN IF NOT EXISTS status TEXT DEFAULT 'ACTIVE' CHECK (status IN ('ACTIVE', 'ON_LEAVE', 'INACTIVE')),
ADD COLUMN IF NOT EXISTS profile_image_url TEXT,
ADD COLUMN IF NOT EXISTS last_login_at TIMESTAMPTZ;

-- DECEASED tablosunu güncelle
ALTER TABLE deceased 
ADD COLUMN IF NOT EXISTS ditib_member_id TEXT,
ADD COLUMN IF NOT EXISTS gender TEXT CHECK (gender IN ('MALE', 'FEMALE')),
ADD COLUMN IF NOT EXISTS date_of_birth DATE,
ADD COLUMN IF NOT EXISTS place_of_death TEXT,
ADD COLUMN IF NOT EXISTS place_of_burial TEXT,
ADD COLUMN IF NOT EXISTS burial_location_latitude DECIMAL(10, 8),
ADD COLUMN IF NOT EXISTS burial_location_longitude DECIMAL(11, 8),
ADD COLUMN IF NOT EXISTS family_contact_name TEXT,
ADD COLUMN IF NOT EXISTS family_email TEXT,
ADD COLUMN IF NOT EXISTS family_phone TEXT,
ADD COLUMN IF NOT EXISTS family_address TEXT;

-- CASES tablosunu güncelle
ALTER TABLE cases 
ADD COLUMN IF NOT EXISTS burial_ceremony_type TEXT CHECK (burial_ceremony_type IN ('RELIGIOUS', 'CIVIL', 'MIXED')),
ADD COLUMN IF NOT EXISTS total_tasks_count INTEGER DEFAULT 0,
ADD COLUMN IF NOT EXISTS completed_tasks_count INTEGER DEFAULT 0,
ADD COLUMN IF NOT EXISTS pending_tasks_count INTEGER DEFAULT 0,
ADD COLUMN IF NOT EXISTS special_instructions TEXT;

-- TASKS tablosunu güncelle
ALTER TABLE tasks 
ADD COLUMN IF NOT EXISTS title TEXT,
ADD COLUMN IF NOT EXISTS description TEXT,
ADD COLUMN IF NOT EXISTS sub_status TEXT,
ADD COLUMN IF NOT EXISTS scheduled_at TIMESTAMPTZ,
ADD COLUMN IF NOT EXISTS due_at TIMESTAMPTZ,
ADD COLUMN IF NOT EXISTS started_at TIMESTAMPTZ,
ADD COLUMN IF NOT EXISTS completed_at TIMESTAMPTZ,
ADD COLUMN IF NOT EXISTS estimated_duration_minutes INTEGER,
ADD COLUMN IF NOT EXISTS actual_duration_minutes INTEGER,
ADD COLUMN IF NOT EXISTS pickup_address TEXT,
ADD COLUMN IF NOT EXISTS delivery_address TEXT,
ADD COLUMN IF NOT EXISTS pickup_latitude DECIMAL(10, 8),
ADD COLUMN IF NOT EXISTS pickup_longitude DECIMAL(11, 8),
ADD COLUMN IF NOT EXISTS delivery_latitude DECIMAL(10, 8),
ADD COLUMN IF NOT EXISTS delivery_longitude DECIMAL(11, 8),
ADD COLUMN IF NOT EXISTS progress_percentage INTEGER DEFAULT 0 CHECK (progress_percentage >= 0 AND progress_percentage <= 100),
ADD COLUMN IF NOT EXISTS completion_notes TEXT,
ADD COLUMN IF NOT EXISTS driver_feedback TEXT;

-- DOCUMENTS tablosunu güncelle
ALTER TABLE documents 
ADD COLUMN IF NOT EXISTS title TEXT,
ADD COLUMN IF NOT EXISTS file_size_bytes BIGINT,
ADD COLUMN IF NOT EXISTS file_mime_type TEXT,
ADD COLUMN IF NOT EXISTS storage_path TEXT,
ADD COLUMN IF NOT EXISTS file_count INTEGER DEFAULT 1,
ADD COLUMN IF NOT EXISTS reviewed_by_id UUID REFERENCES users(id),
ADD COLUMN IF NOT EXISTS reviewed_at TIMESTAMPTZ,
ADD COLUMN IF NOT EXISTS rejection_reason TEXT;

-- TASK_STATUS_HISTORY tablosunu güncelle
ALTER TABLE task_status_history 
ADD COLUMN IF NOT EXISTS old_sub_status TEXT,
ADD COLUMN IF NOT EXISTS new_sub_status TEXT,
ADD COLUMN IF NOT EXISTS old_progress INTEGER,
ADD COLUMN IF NOT EXISTS new_progress INTEGER,
ADD COLUMN IF NOT EXISTS location_latitude DECIMAL(10, 8),
ADD COLUMN IF NOT EXISTS location_longitude DECIMAL(11, 8),
ADD COLUMN IF NOT EXISTS change_reason TEXT;

-- NOTIFICATIONS tablosunu güncelle
ALTER TABLE notifications 
ADD COLUMN IF NOT EXISTS notification_type TEXT NOT NULL DEFAULT 'SYSTEM_ALERT' CHECK (notification_type IN (
  'TASK_ASSIGNED', 'TASK_COMPLETED', 'TASK_OVERDUE', 'CASE_UPDATED',
  'DOCUMENT_UPLOADED', 'DOCUMENT_APPROVED', 'DOCUMENT_REJECTED',
  'FAMILY_MESSAGE', 'SYSTEM_ALERT'
)),
ADD COLUMN IF NOT EXISTS channel TEXT NOT NULL DEFAULT 'IN_APP' CHECK (channel IN ('PUSH', 'SMS', 'EMAIL', 'IN_APP')),
ADD COLUMN IF NOT EXISTS status TEXT NOT NULL DEFAULT 'PENDING' CHECK (status IN ('PENDING', 'SENT', 'DELIVERED', 'FAILED')),
ADD COLUMN IF NOT EXISTS to_address TEXT NOT NULL DEFAULT '',
ADD COLUMN IF NOT EXISTS sent_at TIMESTAMPTZ,
ADD COLUMN IF NOT EXISTS delivered_at TIMESTAMPTZ,
ADD COLUMN IF NOT EXISTS read_at TIMESTAMPTZ;

-- =====================================================
-- ENUM TİPLERİNİ GÜNCELLE
-- =====================================================

-- Task type enum'unu güncelle
DO $$ 
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'task_type_enum') THEN
        CREATE TYPE task_type_enum AS ENUM (
            'PICK_UP_FROM_MORGUE',
            'TO_AIRPORT', 
            'TO_CONSULATE',
            'DELIVERED',
            'DOCUMENT_DELIVERY',
            'FAMILY_MEETING',
            'HOSPITAL_TRANSFER',
            'BURIAL_PREPARATION',
            'CEREMONY_SETUP',
            'TRANSPORT'
        );
    ELSE
        ALTER TYPE task_type_enum ADD VALUE IF NOT EXISTS 'DOCUMENT_DELIVERY';
        ALTER TYPE task_type_enum ADD VALUE IF NOT EXISTS 'FAMILY_MEETING';
        ALTER TYPE task_type_enum ADD VALUE IF NOT EXISTS 'HOSPITAL_TRANSFER';
        ALTER TYPE task_type_enum ADD VALUE IF NOT EXISTS 'BURIAL_PREPARATION';
        ALTER TYPE task_type_enum ADD VALUE IF NOT EXISTS 'CEREMONY_SETUP';
        ALTER TYPE task_type_enum ADD VALUE IF NOT EXISTS 'TRANSPORT';
    END IF;
END $$;

-- Task status enum'unu güncelle
DO $$ 
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'task_status_enum') THEN
        CREATE TYPE task_status_enum AS ENUM (
            'PENDING',
            'IN_PROGRESS',
            'COMPLETED',
            'FAILED',
            'OVERDUE',
            'CANCELLED'
        );
    ELSE
        ALTER TYPE task_status_enum ADD VALUE IF NOT EXISTS 'FAILED';
        ALTER TYPE task_status_enum ADD VALUE IF NOT EXISTS 'OVERDUE';
        ALTER TYPE task_status_enum ADD VALUE IF NOT EXISTS 'CANCELLED';
    END IF;
END $$;

-- Document type enum'unu güncelle
DO $$ 
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'document_type_enum') THEN
        CREATE TYPE document_type_enum AS ENUM (
            'DEATH_CERT',
            'FORMUL_C',
            'CARGO_WAYBILL',
            'PHOTO',
            'DOCUMENT_PHOTO',
            'TRANSPORT_DOCUMENT',
            'NOTES',
            'FAMILY_CONSENT',
            'MEDICAL_REPORT',
            'BURIAL_PERMIT'
        );
    ELSE
        ALTER TYPE document_type_enum ADD VALUE IF NOT EXISTS 'FAMILY_CONSENT';
        ALTER TYPE document_type_enum ADD VALUE IF NOT EXISTS 'MEDICAL_REPORT';
        ALTER TYPE document_type_enum ADD VALUE IF NOT EXISTS 'BURIAL_PERMIT';
    END IF;
END $$;

-- =====================================================
-- VERİ MİGRASYONU
-- =====================================================

-- Users tablosu için varsayılan değerler
UPDATE users 
SET status = 'ACTIVE' 
WHERE status IS NULL;

-- Tasks tablosu için varsayılan değerler ve veri dönüşümü
UPDATE tasks 
SET 
    title = COALESCE(task_type::text, 'Görev'),
    progress_percentage = 0
WHERE title IS NULL;

-- Task type'ları güncelle
UPDATE tasks 
SET task_type = 'PICK_UP_FROM_MORGUE'
WHERE task_type = 'PICKUP';

UPDATE tasks 
SET task_type = 'DELIVERED'
WHERE task_type = 'DELIVERY';

-- Task status'ları güncelle
UPDATE tasks 
SET status = 'PENDING'
WHERE status = 'ASSIGNED';

UPDATE tasks 
SET status = 'IN_PROGRESS'
WHERE status = 'STARTED';

-- Documents tablosu için varsayılan değerler
UPDATE documents 
SET 
    title = COALESCE(file_name, 'Belge'),
    file_count = 1,
    storage_path = COALESCE(file_name, '')
WHERE title IS NULL;

-- Cases tablosu için task sayılarını hesapla
UPDATE cases 
SET 
    total_tasks_count = (
        SELECT COUNT(*) 
        FROM tasks 
        WHERE tasks.case_id = cases.id
    ),
    completed_tasks_count = (
        SELECT COUNT(*) 
        FROM tasks 
        WHERE tasks.case_id = cases.id 
        AND tasks.status = 'COMPLETED'
    ),
    pending_tasks_count = (
        SELECT COUNT(*) 
        FROM tasks 
        WHERE tasks.case_id = cases.id 
        AND tasks.status IN ('PENDING', 'IN_PROGRESS')
    );

-- =====================================================
-- YENİ İNDEXLER OLUŞTUR
-- =====================================================

-- Performance için yeni indexler
CREATE INDEX IF NOT EXISTS idx_tasks_status_priority ON tasks(status, priority);
CREATE INDEX IF NOT EXISTS idx_tasks_assignee_status ON tasks(assignee_id, status);
CREATE INDEX IF NOT EXISTS idx_tasks_due_at ON tasks(due_at) WHERE due_at IS NOT NULL;
CREATE INDEX IF NOT EXISTS idx_tasks_location ON tasks(pickup_latitude, pickup_longitude) WHERE pickup_latitude IS NOT NULL;

CREATE INDEX IF NOT EXISTS idx_cases_status_priority ON cases(status, priority);
CREATE INDEX IF NOT EXISTS idx_cases_assigned_driver ON cases(assigned_driver_id) WHERE assigned_driver_id IS NOT NULL;
CREATE INDEX IF NOT EXISTS idx_cases_family_user ON cases(family_user_id) WHERE family_user_id IS NOT NULL;

CREATE INDEX IF NOT EXISTS idx_documents_case_type ON documents(case_id, document_type);
CREATE INDEX IF NOT EXISTS idx_documents_status ON documents(status);
CREATE INDEX IF NOT EXISTS idx_documents_uploaded_by ON documents(uploaded_by_id);

CREATE INDEX IF NOT EXISTS idx_notifications_user_status ON notifications(user_id, status);
CREATE INDEX IF NOT EXISTS idx_notifications_type_created ON notifications(notification_type, created_at);

CREATE INDEX IF NOT EXISTS idx_task_status_history_task_changed ON task_status_history(task_id, changed_at);

-- =====================================================
-- CONSTRAINT'LERİ GÜNCELLE
-- =====================================================

-- Tasks tablosu için yeni constraint'ler
ALTER TABLE tasks 
ADD CONSTRAINT IF NOT EXISTS chk_tasks_progress_range 
CHECK (progress_percentage >= 0 AND progress_percentage <= 100);

ALTER TABLE tasks 
ADD CONSTRAINT IF NOT EXISTS chk_tasks_duration_positive 
CHECK (estimated_duration_minutes IS NULL OR estimated_duration_minutes > 0);

ALTER TABLE tasks 
ADD CONSTRAINT IF NOT EXISTS chk_tasks_actual_duration_positive 
CHECK (actual_duration_minutes IS NULL OR actual_duration_minutes > 0);

-- Cases tablosu için yeni constraint'ler
ALTER TABLE cases 
ADD CONSTRAINT IF NOT EXISTS chk_cases_progress_range 
CHECK (progress_percentage >= 0 AND progress_percentage <= 100);

ALTER TABLE cases 
ADD CONSTRAINT IF NOT EXISTS chk_cases_task_counts_positive 
CHECK (total_tasks_count >= 0 AND completed_tasks_count >= 0 AND pending_tasks_count >= 0);

ALTER TABLE cases 
ADD CONSTRAINT IF NOT EXISTS chk_cases_task_counts_logical 
CHECK (completed_tasks_count + pending_tasks_count <= total_tasks_count);

-- Documents tablosu için yeni constraint'ler
ALTER TABLE documents 
ADD CONSTRAINT IF NOT EXISTS chk_documents_file_size_positive 
CHECK (file_size_bytes IS NULL OR file_size_bytes > 0);

ALTER TABLE documents 
ADD CONSTRAINT IF NOT EXISTS chk_documents_file_count_positive 
CHECK (file_count > 0);

-- =====================================================
-- TRİGGER'LARI GÜNCELLE
-- =====================================================

-- Updated_at trigger'ını tüm tablolara ekle
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Mevcut trigger'ları kaldır ve yeniden oluştur
DROP TRIGGER IF EXISTS update_users_updated_at ON users;
DROP TRIGGER IF EXISTS update_deceased_updated_at ON deceased;
DROP TRIGGER IF EXISTS update_cases_updated_at ON cases;
DROP TRIGGER IF EXISTS update_tasks_updated_at ON tasks;
DROP TRIGGER IF EXISTS update_documents_updated_at ON documents;

CREATE TRIGGER update_users_updated_at BEFORE UPDATE ON users FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_deceased_updated_at BEFORE UPDATE ON deceased FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_cases_updated_at BEFORE UPDATE ON cases FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_tasks_updated_at BEFORE UPDATE ON tasks FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_documents_updated_at BEFORE UPDATE ON documents FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Case progress hesaplama trigger'ı
CREATE OR REPLACE FUNCTION update_case_progress()
RETURNS TRIGGER AS $$
BEGIN
    -- Case'in task sayılarını ve progress'ini güncelle
    UPDATE cases 
    SET 
        total_tasks_count = (
            SELECT COUNT(*) 
            FROM tasks 
            WHERE tasks.case_id = COALESCE(NEW.case_id, OLD.case_id)
        ),
        completed_tasks_count = (
            SELECT COUNT(*) 
            FROM tasks 
            WHERE tasks.case_id = COALESCE(NEW.case_id, OLD.case_id)
            AND tasks.status = 'COMPLETED'
        ),
        pending_tasks_count = (
            SELECT COUNT(*) 
            FROM tasks 
            WHERE tasks.case_id = COALESCE(NEW.case_id, OLD.case_id)
            AND tasks.status IN ('PENDING', 'IN_PROGRESS')
        ),
        progress_percentage = (
            SELECT CASE 
                WHEN COUNT(*) = 0 THEN 0
                ELSE ROUND((COUNT(*) FILTER (WHERE status = 'COMPLETED') * 100.0) / COUNT(*))
            END
            FROM tasks 
            WHERE tasks.case_id = COALESCE(NEW.case_id, OLD.case_id)
        ),
        updated_at = CURRENT_TIMESTAMP
    WHERE id = COALESCE(NEW.case_id, OLD.case_id);
    
    RETURN COALESCE(NEW, OLD);
END;
$$ language 'plpgsql';

DROP TRIGGER IF EXISTS update_case_progress_on_task_change ON tasks;
CREATE TRIGGER update_case_progress_on_task_change 
AFTER INSERT OR UPDATE OR DELETE ON tasks 
FOR EACH ROW EXECUTE FUNCTION update_case_progress();

-- Task status history trigger'ı
CREATE OR REPLACE FUNCTION log_task_status_change()
RETURNS TRIGGER AS $$
BEGIN
    -- Sadece status, sub_status veya progress değiştiğinde log'la
    IF (OLD.status IS DISTINCT FROM NEW.status) OR 
       (OLD.sub_status IS DISTINCT FROM NEW.sub_status) OR 
       (OLD.progress_percentage IS DISTINCT FROM NEW.progress_percentage) THEN
        
        INSERT INTO task_status_history (
            task_id, actor_id, old_status, new_status, 
            old_sub_status, new_sub_status,
            old_progress, new_progress, changed_at
        ) VALUES (
            NEW.id, NEW.assignee_id, OLD.status, NEW.status,
            OLD.sub_status, NEW.sub_status,
            OLD.progress_percentage, NEW.progress_percentage,
            CURRENT_TIMESTAMP
        );
    END IF;
    
    RETURN NEW;
END;
$$ language 'plpgsql';

DROP TRIGGER IF EXISTS log_task_status_change_trigger ON tasks;
CREATE TRIGGER log_task_status_change_trigger 
AFTER UPDATE ON tasks 
FOR EACH ROW EXECUTE FUNCTION log_task_status_change();

-- =====================================================
-- VERİ DOĞRULAMA
-- =====================================================

-- Migrasyon sonrası veri doğrulama
DO $$
DECLARE
    total_users INTEGER;
    total_cases INTEGER;
    total_tasks INTEGER;
    total_documents INTEGER;
BEGIN
    SELECT COUNT(*) INTO total_users FROM users;
    SELECT COUNT(*) INTO total_cases FROM cases;
    SELECT COUNT(*) INTO total_tasks FROM tasks;
    SELECT COUNT(*) INTO total_documents FROM documents;
    
    RAISE NOTICE 'Migrasyon tamamlandı:';
    RAISE NOTICE '- Toplam kullanıcı: %', total_users;
    RAISE NOTICE '- Toplam vaka: %', total_cases;
    RAISE NOTICE '- Toplam görev: %', total_tasks;
    RAISE NOTICE '- Toplam belge: %', total_documents;
    
    -- Veri tutarlılığı kontrolleri
    IF EXISTS (SELECT 1 FROM tasks WHERE progress_percentage < 0 OR progress_percentage > 100) THEN
        RAISE WARNING 'Bazı görevlerde geçersiz progress değerleri bulundu!';
    END IF;
    
    IF EXISTS (SELECT 1 FROM cases WHERE completed_tasks_count + pending_tasks_count > total_tasks_count) THEN
        RAISE WARNING 'Bazı vakalarda tutarsız görev sayıları bulundu!';
    END IF;
    
    RAISE NOTICE 'Migrasyon başarıyla tamamlandı!';
END $$;

-- =====================================================
-- YEDEK TABLOLARI TEMİZLE (İSTEĞE BAĞLI)
-- =====================================================

-- Migrasyon başarılı olduktan sonra yedek tabloları silebilirsiniz:
-- DROP TABLE IF EXISTS backup_users;
-- DROP TABLE IF EXISTS backup_deceased;
-- DROP TABLE IF EXISTS backup_cases;
-- DROP TABLE IF EXISTS backup_tasks;
-- DROP TABLE IF EXISTS backup_documents;
-- DROP TABLE IF EXISTS backup_task_status_history;
-- DROP TABLE IF EXISTS backup_notifications;
-- DROP TABLE IF EXISTS backup_lookup_values;